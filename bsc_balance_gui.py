import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from web3 import Web3
import json
import csv
from datetime import datetime
import os

# BSC Network Configuration
BSC_RPC_URL = "https://bsc-dataseed1.binance.org/"
BSC_CHAIN_ID = 56

# Token Contract Addresses on BSC
USDC_BSC_CONTRACT = "******************************************"
USDT_BSC_CONTRACT = "******************************************"

# Default special wallets
DEFAULT_SPECIAL_WALLETS = [
    "******************************************",
    "******************************************"
]

# Standard ERC20 ABI for balance checking
ERC20_ABI = [
    {
        "constant": True,
        "inputs": [{"name": "_owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "balance", "type": "uint256"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    }
]

class BSCWalletManager:
    def __init__(self, root):
        self.root = root
        self.root.title("BSC Wallet Manager")
        self.root.geometry("900x700")

        # Initialize Web3
        self.web3 = None
        self.usdc_contract = None
        self.usdt_contract = None

        self.setup_ui()
        
    def setup_ui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Balance Checker Tab
        self.balance_frame = ttk.Frame(notebook)
        notebook.add(self.balance_frame, text="Balance Checker")
        self.setup_balance_tab()

        # Wallet Generator Tab
        self.generator_frame = ttk.Frame(notebook)
        notebook.add(self.generator_frame, text="Wallet Generator")
        self.setup_generator_tab()

    def setup_balance_tab(self):
        # Configure grid weights
        self.balance_frame.columnconfigure(1, weight=1)
        self.balance_frame.rowconfigure(3, weight=1)

        # Title
        title_label = ttk.Label(self.balance_frame, text="BSC Balance Checker", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(10, 20))

        # Wallet addresses input
        ttk.Label(self.balance_frame, text="Wallet Addresses (one per line):").grid(row=1, column=0, sticky=tk.W, padx=10, pady=(0, 5))

        self.wallet_text = scrolledtext.ScrolledText(self.balance_frame, height=8, width=60)
        self.wallet_text.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Pre-fill with default wallets
        default_wallets = "\n".join(DEFAULT_SPECIAL_WALLETS)
        self.wallet_text.insert("1.0", default_wallets)

        # Buttons frame
        button_frame = ttk.Frame(self.balance_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        self.check_button = ttk.Button(button_frame, text="Check Balances", command=self.start_balance_check)
        self.check_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(button_frame, text="Clear Results", command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)

        # Progress bar
        self.progress = ttk.Progressbar(self.balance_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Results area
        ttk.Label(self.balance_frame, text="Results:").grid(row=5, column=0, sticky=tk.W, padx=10, pady=(0, 5))

        self.results_text = scrolledtext.ScrolledText(self.balance_frame, height=15, width=80, font=("Consolas", 10))
        self.results_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=(0, 10))

    def setup_generator_tab(self):
        # Configure grid weights
        self.generator_frame.columnconfigure(1, weight=1)
        self.generator_frame.rowconfigure(4, weight=1)

        # Title
        title_label = ttk.Label(self.generator_frame, text="Wallet Generator", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(10, 20))

        # Number of wallets input
        input_frame = ttk.Frame(self.generator_frame)
        input_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        ttk.Label(input_frame, text="Number of wallets to generate:").pack(side=tk.LEFT, padx=(0, 10))

        self.num_wallets_var = tk.StringVar(value="10")
        self.num_wallets_entry = ttk.Entry(input_frame, textvariable=self.num_wallets_var, width=10)
        self.num_wallets_entry.pack(side=tk.LEFT, padx=(0, 20))

        # Generator buttons
        gen_button_frame = ttk.Frame(self.generator_frame)
        gen_button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        self.generate_button = ttk.Button(gen_button_frame, text="Generate Wallets", command=self.start_wallet_generation)
        self.generate_button.pack(side=tk.LEFT, padx=(0, 10))

        self.save_button = ttk.Button(gen_button_frame, text="Save to CSV", command=self.save_wallets_to_csv)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_gen_button = ttk.Button(gen_button_frame, text="Clear", command=self.clear_generated_wallets)
        self.clear_gen_button.pack(side=tk.LEFT)

        # Progress bar for generation
        self.gen_progress = ttk.Progressbar(self.generator_frame, mode='indeterminate')
        self.gen_progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Generated wallets display
        ttk.Label(self.generator_frame, text="Generated Wallets:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=(0, 5))

        self.generated_text = scrolledtext.ScrolledText(self.generator_frame, height=20, width=80, font=("Consolas", 9))
        self.generated_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=(0, 10))
        
    def log_message(self, message):
        """Add message to results area"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete("1.0", tk.END)
        
    def get_bnb_balance(self, address):
        """Get BNB balance for a given address"""
        try:
            balance_wei = self.web3.eth.get_balance(address)
            balance_bnb = self.web3.from_wei(balance_wei, 'ether')
            return float(balance_bnb)
        except Exception as e:
            self.log_message(f"Error getting BNB balance for {address}: {str(e)}")
            return 0.0
    
    def get_token_balance(self, address, token_contract, token_name):
        """Get token balance for a given address"""
        try:
            balance_raw = token_contract.functions.balanceOf(address).call()
            decimals = token_contract.functions.decimals().call()
            balance_token = balance_raw / (10 ** decimals)
            return float(balance_token)
        except Exception as e:
            self.log_message(f"Error getting {token_name} balance for {address}: {str(e)}")
            return 0.0
    
    def check_wallet_balance(self, address):
        """Check BNB, USDC, and USDT balance for a single wallet"""
        bnb_balance = self.get_bnb_balance(address)
        usdc_balance = self.get_token_balance(address, self.usdc_contract, "USDC")
        usdt_balance = self.get_token_balance(address, self.usdt_contract, "USDT")
        
        return {
            'address': address,
            'bnb_balance': bnb_balance,
            'usdc_balance': usdc_balance,
            'usdt_balance': usdt_balance
        }
    
    def generate_wallets(self, num_wallets):
        """Generate specified number of wallets"""
        wallets = []

        for i in range(num_wallets):
            # Generate a new account
            account = Web3().eth.account.create()

            wallet_info = {
                'no': i + 1,
                'address': account.address,
                'private_key': account.key.hex()
            }
            wallets.append(wallet_info)

            # Update progress in UI
            self.log_generated_wallet(f"Generated wallet {i+1}/{num_wallets}: {account.address}")

        return wallets

    def log_generated_wallet(self, message):
        """Add message to generated wallets area"""
        self.generated_text.insert(tk.END, message + "\n")
        self.generated_text.see(tk.END)
        self.root.update_idletasks()

    def clear_generated_wallets(self):
        """Clear the generated wallets area"""
        self.generated_text.delete("1.0", tk.END)

    def start_wallet_generation(self):
        """Start wallet generation in a separate thread"""
        try:
            num_wallets = int(self.num_wallets_var.get())
            if num_wallets <= 0:
                messagebox.showerror("Error", "Number of wallets must be positive")
                return
            if num_wallets > 1000:
                messagebox.showerror("Error", "Maximum 1000 wallets allowed")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")
            return

        # Disable button during generation
        self.generate_button.config(state='disabled')
        self.gen_progress.start()

        # Start generation in background thread
        thread = threading.Thread(target=self.generate_wallets_thread, args=(num_wallets,))
        thread.daemon = True
        thread.start()

    def generate_wallets_thread(self, num_wallets):
        """Wallet generation thread"""
        try:
            self.log_generated_wallet(f"Generating {num_wallets} wallets...")
            self.log_generated_wallet("=" * 50)

            self.generated_wallets = self.generate_wallets(num_wallets)

            self.log_generated_wallet("=" * 50)
            self.log_generated_wallet(f"Successfully generated {len(self.generated_wallets)} wallets!")
            self.log_generated_wallet("\nWallet Details:")
            self.log_generated_wallet("-" * 30)

            for wallet in self.generated_wallets:
                self.log_generated_wallet(f"No: {wallet['no']}")
                self.log_generated_wallet(f"Address: {wallet['address']}")
                self.log_generated_wallet(f"Private Key: {wallet['private_key']}")
                self.log_generated_wallet("-" * 30)

            self.log_generated_wallet("\nIMPORTANT: Keep your private keys secure and never share them!")

        except Exception as e:
            self.log_generated_wallet(f"Error generating wallets: {str(e)}")
            messagebox.showerror("Error", f"Error generating wallets: {str(e)}")

        finally:
            # Re-enable button and stop progress
            self.generate_button.config(state='normal')
            self.gen_progress.stop()

    def save_wallets_to_csv(self):
        """Save generated wallets to CSV file"""
        if not hasattr(self, 'generated_wallets') or not self.generated_wallets:
            messagebox.showwarning("Warning", "No wallets generated yet!")
            return

        # Generate filename with current date and time
        current_time = datetime.now()
        default_filename = f"wallets_{current_time.year}_{current_time.month}_{current_time.day}_{current_time.hour}_{current_time.minute}_{current_time.second}.csv"

        # Ask user for save location
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialname=default_filename
        )

        if filename:
            try:
                with open(filename, 'w', newline='') as f:
                    writer = csv.writer(f)
                    for wallet in self.generated_wallets:
                        writer.writerow([wallet['no'], wallet['address'], wallet['private_key']])

                messagebox.showinfo("Success", f"Wallets saved to {filename}")
                self.log_generated_wallet(f"\nWallets saved to: {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Error saving file: {str(e)}")

    def start_balance_check(self):
        """Start balance checking in a separate thread"""
        # Disable button during check
        self.check_button.config(state='disabled')
        self.progress.start()

        # Start checking in background thread
        thread = threading.Thread(target=self.check_balances)
        thread.daemon = True
        thread.start()
    
    def check_balances(self):
        """Main balance checking logic"""
        try:
            self.log_message("Connecting to Binance Smart Chain...")
            
            # Initialize Web3 connection
            self.web3 = Web3(Web3.HTTPProvider(BSC_RPC_URL))
            
            if not self.web3.is_connected():
                self.log_message("Error: Could not connect to BSC network")
                return
            
            self.log_message(f"Connected to BSC! Chain ID: {self.web3.eth.chain_id}")
            
            # Initialize token contracts
            self.usdc_contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(USDC_BSC_CONTRACT),
                abi=ERC20_ABI
            )
            self.usdt_contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(USDT_BSC_CONTRACT),
                abi=ERC20_ABI
            )
            
            # Get wallet addresses from text input
            wallet_text = self.wallet_text.get("1.0", tk.END).strip()
            wallet_addresses = [addr.strip() for addr in wallet_text.split('\n') if addr.strip()]
            
            if not wallet_addresses:
                self.log_message("No wallet addresses provided!")
                return
            
            self.log_message("\n" + "="*60)
            self.log_message("BSC BALANCE CHECKER")
            self.log_message("="*60)
            
            # Check each wallet
            total_bnb = 0.0
            total_usdc = 0.0
            total_usdt = 0.0
            
            for i, address in enumerate(wallet_addresses, 1):
                try:
                    self.log_message(f"\nWallet {i}:")
                    balance = self.check_wallet_balance(address)
                    
                    self.log_message(f"  Address: {balance['address']}")
                    self.log_message(f"  BNB Balance: {balance['bnb_balance']:.6f} BNB")
                    self.log_message(f"  USDC Balance: {balance['usdc_balance']:.6f} USDC")
                    self.log_message(f"  USDT Balance: {balance['usdt_balance']:.6f} USDT")
                    
                    total_bnb += balance['bnb_balance']
                    total_usdc += balance['usdc_balance']
                    total_usdt += balance['usdt_balance']
                    
                except Exception as e:
                    self.log_message(f"  Error checking wallet {address}: {str(e)}")
            
            # Show summary
            self.log_message("\n" + "="*60)
            self.log_message("SUMMARY")
            self.log_message("="*60)
            self.log_message(f"Wallets Checked: {len(wallet_addresses)}")
            self.log_message(f"Total BNB: {total_bnb:.6f} BNB")
            self.log_message(f"Total USDC: {total_usdc:.6f} USDC")
            self.log_message(f"Total USDT: {total_usdt:.6f} USDT")
            
            self.log_message("\nBalance check completed!")
            
        except Exception as e:
            self.log_message(f"An error occurred: {str(e)}")
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        
        finally:
            # Re-enable button and stop progress
            self.check_button.config(state='normal')
            self.progress.stop()

def main():
    root = tk.Tk()
    app = BSCWalletManager(root)
    root.mainloop()

if __name__ == "__main__":
    main()
