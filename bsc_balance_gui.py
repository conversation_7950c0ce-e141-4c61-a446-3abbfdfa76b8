import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from web3 import Web3
import json

# BSC Network Configuration
BSC_RPC_URL = "https://bsc-dataseed1.binance.org/"
BSC_CHAIN_ID = 56

# Token Contract Addresses on BSC
USDC_BSC_CONTRACT = "******************************************"
USDT_BSC_CONTRACT = "******************************************"

# Default special wallets
DEFAULT_SPECIAL_WALLETS = [
    "******************************************",
    "******************************************"
]

# Standard ERC20 ABI for balance checking
ERC20_ABI = [
    {
        "constant": True,
        "inputs": [{"name": "_owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"name": "balance", "type": "uint256"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "decimals",
        "outputs": [{"name": "", "type": "uint8"}],
        "type": "function"
    },
    {
        "constant": True,
        "inputs": [],
        "name": "symbol",
        "outputs": [{"name": "", "type": "string"}],
        "type": "function"
    }
]

class BSCBalanceChecker:
    def __init__(self, root):
        self.root = root
        self.root.title("BSC Balance Checker")
        self.root.geometry("800x600")
        
        # Initialize Web3
        self.web3 = None
        self.usdc_contract = None
        self.usdt_contract = None
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="BSC Balance Checker", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Wallet addresses input
        ttk.Label(main_frame, text="Wallet Addresses (one per line):").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        self.wallet_text = scrolledtext.ScrolledText(main_frame, height=8, width=60)
        self.wallet_text.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Pre-fill with default wallets
        default_wallets = "\n".join(DEFAULT_SPECIAL_WALLETS)
        self.wallet_text.insert("1.0", default_wallets)
        
        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.check_button = ttk.Button(button_frame, text="Check Balances", command=self.start_balance_check)
        self.check_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="Clear Results", command=self.clear_results)
        self.clear_button.pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results area
        ttk.Label(main_frame, text="Results:").grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        
        self.results_text = scrolledtext.ScrolledText(main_frame, height=15, width=80, font=("Consolas", 10))
        self.results_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def log_message(self, message):
        """Add message to results area"""
        self.results_text.insert(tk.END, message + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_results(self):
        """Clear the results area"""
        self.results_text.delete("1.0", tk.END)
        
    def get_bnb_balance(self, address):
        """Get BNB balance for a given address"""
        try:
            balance_wei = self.web3.eth.get_balance(address)
            balance_bnb = self.web3.from_wei(balance_wei, 'ether')
            return float(balance_bnb)
        except Exception as e:
            self.log_message(f"Error getting BNB balance for {address}: {str(e)}")
            return 0.0
    
    def get_token_balance(self, address, token_contract, token_name):
        """Get token balance for a given address"""
        try:
            balance_raw = token_contract.functions.balanceOf(address).call()
            decimals = token_contract.functions.decimals().call()
            balance_token = balance_raw / (10 ** decimals)
            return float(balance_token)
        except Exception as e:
            self.log_message(f"Error getting {token_name} balance for {address}: {str(e)}")
            return 0.0
    
    def check_wallet_balance(self, address):
        """Check BNB, USDC, and USDT balance for a single wallet"""
        bnb_balance = self.get_bnb_balance(address)
        usdc_balance = self.get_token_balance(address, self.usdc_contract, "USDC")
        usdt_balance = self.get_token_balance(address, self.usdt_contract, "USDT")
        
        return {
            'address': address,
            'bnb_balance': bnb_balance,
            'usdc_balance': usdc_balance,
            'usdt_balance': usdt_balance
        }
    
    def start_balance_check(self):
        """Start balance checking in a separate thread"""
        # Disable button during check
        self.check_button.config(state='disabled')
        self.progress.start()
        
        # Start checking in background thread
        thread = threading.Thread(target=self.check_balances)
        thread.daemon = True
        thread.start()
    
    def check_balances(self):
        """Main balance checking logic"""
        try:
            self.log_message("Connecting to Binance Smart Chain...")
            
            # Initialize Web3 connection
            self.web3 = Web3(Web3.HTTPProvider(BSC_RPC_URL))
            
            if not self.web3.is_connected():
                self.log_message("Error: Could not connect to BSC network")
                return
            
            self.log_message(f"Connected to BSC! Chain ID: {self.web3.eth.chain_id}")
            
            # Initialize token contracts
            self.usdc_contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(USDC_BSC_CONTRACT),
                abi=ERC20_ABI
            )
            self.usdt_contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(USDT_BSC_CONTRACT),
                abi=ERC20_ABI
            )
            
            # Get wallet addresses from text input
            wallet_text = self.wallet_text.get("1.0", tk.END).strip()
            wallet_addresses = [addr.strip() for addr in wallet_text.split('\n') if addr.strip()]
            
            if not wallet_addresses:
                self.log_message("No wallet addresses provided!")
                return
            
            self.log_message("\n" + "="*60)
            self.log_message("BSC BALANCE CHECKER")
            self.log_message("="*60)
            
            # Check each wallet
            total_bnb = 0.0
            total_usdc = 0.0
            total_usdt = 0.0
            
            for i, address in enumerate(wallet_addresses, 1):
                try:
                    self.log_message(f"\nWallet {i}:")
                    balance = self.check_wallet_balance(address)
                    
                    self.log_message(f"  Address: {balance['address']}")
                    self.log_message(f"  BNB Balance: {balance['bnb_balance']:.6f} BNB")
                    self.log_message(f"  USDC Balance: {balance['usdc_balance']:.6f} USDC")
                    self.log_message(f"  USDT Balance: {balance['usdt_balance']:.6f} USDT")
                    
                    total_bnb += balance['bnb_balance']
                    total_usdc += balance['usdc_balance']
                    total_usdt += balance['usdt_balance']
                    
                except Exception as e:
                    self.log_message(f"  Error checking wallet {address}: {str(e)}")
            
            # Show summary
            self.log_message("\n" + "="*60)
            self.log_message("SUMMARY")
            self.log_message("="*60)
            self.log_message(f"Wallets Checked: {len(wallet_addresses)}")
            self.log_message(f"Total BNB: {total_bnb:.6f} BNB")
            self.log_message(f"Total USDC: {total_usdc:.6f} USDC")
            self.log_message(f"Total USDT: {total_usdt:.6f} USDT")
            
            self.log_message("\nBalance check completed!")
            
        except Exception as e:
            self.log_message(f"An error occurred: {str(e)}")
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
        
        finally:
            # Re-enable button and stop progress
            self.check_button.config(state='normal')
            self.progress.stop()

def main():
    root = tk.Tk()
    app = BSCBalanceChecker(root)
    root.mainloop()

if __name__ == "__main__":
    main()
