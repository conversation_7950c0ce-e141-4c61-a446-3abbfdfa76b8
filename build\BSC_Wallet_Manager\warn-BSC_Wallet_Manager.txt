
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named org - imported by pickle (optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _manylinux - imported by pkg_resources._vendor.packaging._manylinux (delayed, optional), packaging._manylinux (delayed, optional)
missing module named jinja2 - imported by pyparsing.diagram (top-level), pkg_resources._vendor.pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named railroad - imported by pkg_resources._vendor.pyparsing.diagram (top-level), pyparsing.diagram (top-level)
missing module named termios - imported by tty (top-level), getpass (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), websockets.cli (delayed, optional)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named 'com.sun' - imported by pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named com - imported by pkg_resources._vendor.appdirs (delayed)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named coincurve - imported by eth_keys.backends.coincurve (delayed, optional)
missing module named cytoolz.identity - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional), eth_account._utils.validation (top-level), eth_account._utils.typed_transactions (top-level)
missing module named cytoolz.valmap - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.valfilter - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.update_in - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.unique - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.topk - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.take_nth - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.take - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.tail - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.sliding_window - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.remove - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.reduceby - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.random_sample - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.pluck - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.partitionby - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.partition_all - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.partition - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.nth - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.merge_with - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.merge - imported by cytoolz (optional), eth_utils.toolz (optional), eth_account._utils.legacy_transactions (top-level), eth_account._utils.typed_transactions (top-level)
missing module named cytoolz.mapcat - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.keymap - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.keyfilter - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.join - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.iterate - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.itemmap - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.itemfilter - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.interpose - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.groupby - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.get_in - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.get - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.excepts - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.drop - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.do - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.countby - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.cons - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.assoc_in - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.assoc - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.accumulate - imported by cytoolz (optional), eth_utils.toolz (optional)
missing module named cytoolz.dissoc - imported by cytoolz (top-level), eth_account.account (top-level), eth_utils.toolz (optional), eth_account._utils.legacy_transactions (top-level), eth_account._utils.typed_transactions (top-level)
missing module named cytoolz.thread_last - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.thread_first - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.second - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.pipe - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional), eth_account._utils.legacy_transactions (top-level), eth_account._utils.typed_transactions (top-level), eth_account._utils.signing (top-level)
missing module named cytoolz.peek - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.merge_sorted - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.last - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.juxt - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.isiterable - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.isdistinct - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.interleave - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.frequencies - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.first - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.diff - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.curry - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional), eth_account._utils.legacy_transactions (top-level)
missing module named cytoolz.count - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.concatv - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.concat - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.compose_left - imported by cytoolz (top-level), cytoolz.curried (top-level)
missing module named cytoolz.compose - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.complement - imported by cytoolz (top-level), cytoolz.curried (top-level), eth_utils.toolz (optional)
missing module named cytoolz.apply - imported by cytoolz (top-level), cytoolz.curried (top-level)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by aiohttp.compression_utils (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by aiohttp.compression_utils (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named sha3 - imported by eth_hash.backends.pysha3 (top-level)
missing module named cffi - imported by Crypto.Util._raw_api (optional)
missing module named StringIO - imported by Crypto.Util.py3compat (conditional)
missing module named rusty_rlp - imported by rlp.codec (optional)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named _typeshed - imported by pydantic._internal._dataclasses (conditional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), eth_utils.pydantic (top-level)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named tomli - imported by pydantic.mypy (delayed, conditional, optional), pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named 'werkzeug.routing' - imported by websockets.asyncio.router (top-level), websockets.sync.router (top-level)
missing module named 'werkzeug.exceptions' - imported by websockets.sync.router (top-level)
missing module named 'python_socks.sync' - imported by websockets.sync.client (optional)
missing module named python_socks - imported by websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named werkzeug - imported by websockets.asyncio.router (top-level)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named eth_typing.Manifest - imported by eth_typing (top-level), web3.pm (top-level), ethpm.package (top-level)
missing module named eth_typing.ContractName - imported by eth_typing (top-level), web3.pm (top-level), ethpm.package (top-level)
missing module named 'eth_tester.exceptions' - imported by web3.providers.eth_tester.defaults (top-level), web3.providers.eth_tester.main (delayed)
missing module named 'eth_tester.backends' - imported by web3.providers.eth_tester.main (delayed, conditional)
missing module named eth_tester - imported by web3.providers.eth_tester.main (delayed, conditional), web3.providers.eth_tester.defaults (delayed, conditional)
missing module named base58 - imported by ethpm._utils.ipfs (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named jsonpointer - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named importlib_resources - imported by jsonschema_specifications._core (optional)
missing module named 'ipfshttpclient.exceptions' - imported by ethpm._utils.backend (optional)
missing module named ipfshttpclient - imported by ethpm.backends.ipfs (optional)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level)
missing module named IPython - imported by dotenv.ipython (top-level)
