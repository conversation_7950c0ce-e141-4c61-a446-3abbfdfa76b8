BSC Wallet Manager
==================

This is a comprehensive GUI application for BSC wallet management with two main features:

BALANCE CHECKER:
- Check BNB, USDC, and USDT balances on Binance Smart Chain
- Enter wallet addresses (one per line) and get real-time balances
- Shows individual wallet balances and totals

WALLET GENERATOR:
- Generate new BSC/Ethereum wallets
- Specify number of wallets to create (1-1000)
- View generated addresses and private keys
- Save wallets to CSV file for backup

How to use:
1. Run the BSC_Wallet_Manager executable
2. Use the tabs to switch between Balance Checker and Wallet Generator
3. Follow the instructions in each tab

SECURITY WARNING:
- Keep your private keys secure and never share them
- Back up your wallets safely
- This application connects to BSC mainnet for real data

Features:
- Dual-tab interface for easy navigation
- Real-time balance checking
- Secure wallet generation
- CSV export functionality
- Progress indicators
- Error handling and validation
